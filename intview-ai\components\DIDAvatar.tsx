"use client";
import React, { useState, useRef, useEffect } from "react";
import { Bot, Loader2 } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

const DID_API_URL = "https://api.d-id.com/talks";

interface DIDAvatarProps {
  text?: string;
  onVideoReady?: () => void;
  onVideoEnd?: () => void;
  className?: string;
  isLoading?: boolean;
}

const DIDAvatar: React.FC<DIDAvatarProps> = ({
  text,
  onVideoReady,
  onVideoEnd,
  className = "",
  isLoading = false,
}) => {
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  const createTalk = async (inputText: string) => {
    if (!inputText.trim()) return;

    setLoading(true);
    setVideoUrl(null);
    setError(null);

    const payload = {
      script: {
        type: "text",
        input: inputText.trim(),
      },
      // source_url: "https://i.imgur.com/22232650.png",//
    };

    try {
      const response = await fetch(DID_API_URL, {
        method: "POST",
        headers: {
          Authorization: `Basic ${btoa(process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || "")}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`Failed to create talk: ${response.statusText}`);
      }

      const data = await response.json();
      const talkId = data.id;

      // Poll for video completion
      let videoUrlResponse: string | null = null;
      let attempts = 0;
      const maxAttempts = 30; // 90 seconds max wait time

      while (!videoUrlResponse && attempts < maxAttempts) {
        const pollResp = await fetch(`${DID_API_URL}/${talkId}`, {
          headers: {
            Authorization: `Basic ${btoa(process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || "")}`,
            "Content-Type": "application/json",
          },
        });

        const pollData = await pollResp.json();

        if (pollData.status === "done" && pollData.result_url) {
          videoUrlResponse = pollData.result_url;
        } else if (pollData.status === "error") {
          throw new Error("Video generation failed");
        } else {
          await new Promise((res) => setTimeout(res, 3000));
          attempts++;
        }
      }

      if (!videoUrlResponse) {
        throw new Error("Video generation timed out");
      }

      setVideoUrl(videoUrlResponse);
      onVideoReady?.();
    } catch (err: any) {
      console.error("D-ID API Error:", err);
      setError(err.message || "Failed to generate video");
    } finally {
      setLoading(false);
    }
  };

  // Effect to create video when text changes
  useEffect(() => {
    if (text && text.trim()) {
      createTalk(text);
    }
  }, [text]);

  // Handle video end event
  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      const handleVideoEnd = () => {
        onVideoEnd?.();
      };

      video.addEventListener("ended", handleVideoEnd);
      return () => {
        video.removeEventListener("ended", handleVideoEnd);
      };
    }
  }, [videoUrl, onVideoEnd]);

  return (
    <div className={`relative w-full h-full ${className}`}>
      <AnimatePresence mode="wait">
        {videoUrl && !loading ? (
          <motion.div
            key="video"
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            transition={{ duration: 0.5 }}
            className="w-full h-full relative"
          >
            <video
              ref={videoRef}
              src={videoUrl}
              autoPlay
              className="w-full h-full object-cover rounded-lg"
              onLoadedData={() => onVideoReady?.()}
            />
            {/* Speaking indicator */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="absolute top-4 right-4 flex items-center space-x-2 bg-black/20 backdrop-blur-sm rounded-full px-3 py-2"
            >
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-white text-sm font-medium">Speaking</span>
            </motion.div>
          </motion.div>
        ) : (
          <motion.div
            key="placeholder"
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            transition={{ duration: 0.5 }}
            className="w-full h-full bg-gradient-to-br from-blue-400 via-indigo-500 to-purple-600 flex flex-col items-center justify-center relative rounded-lg"
          >
            {/* Large Bot Icon */}
            <Bot className="w-16 h-16 sm:w-24 sm:h-24 lg:w-32 lg:h-32 text-white/90 mb-4 lg:mb-8" />

            {/* Avatar Info */}
            <div className="text-center text-white px-4">
              <h3 className="text-lg sm:text-xl lg:text-2xl font-bold mb-1 lg:mb-2">
                AI Interviewer
              </h3>
              <div className="flex items-center justify-center space-x-2 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 lg:px-4 lg:py-2">
                <div className="w-2 h-2 lg:w-3 lg:h-3 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-white font-medium text-sm lg:text-base">
                  {loading || isLoading ? "Preparing..." : "Ready"}
                </span>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute top-4 left-4 lg:top-8 lg:left-8 w-8 h-8 lg:w-16 lg:h-16 bg-white/10 rounded-full blur-xl"></div>
            <div className="absolute bottom-6 right-6 lg:bottom-12 lg:right-12 w-12 h-12 lg:w-24 lg:h-24 bg-white/5 rounded-full blur-2xl"></div>
            <div className="absolute top-1/3 right-4 lg:right-8 w-4 h-4 lg:w-8 lg:h-8 bg-white/20 rounded-full blur-sm"></div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Loading Overlay */}
      {(loading || isLoading) && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/40 backdrop-blur-sm flex flex-col items-center justify-center rounded-lg"
        >
          <div className="bg-white/90 backdrop-blur-sm rounded-xl lg:rounded-2xl p-4 sm:p-6 lg:p-8 text-center shadow-2xl mx-4 max-w-sm">
            <Loader2 className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 animate-spin text-indigo-600 mx-auto mb-3 lg:mb-4" />
            <p className="text-sm sm:text-base text-gray-600 mb-4 lg:mb-6">
              Preparing your question...
            </p>

            {/* Progress bar */}
            <div className="w-48 sm:w-56 lg:w-64 h-1.5 lg:h-2 bg-gray-200 rounded-full overflow-hidden mx-auto">
              <motion.div
                className="h-full bg-gradient-to-r from-blue-500 to-indigo-600"
                initial={{ width: 0 }}
                animate={{ width: "100%" }}
                transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
              />
            </div>
          </div>
        </motion.div>
      )}

      {/* Error State */}
      {error && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute inset-0 bg-red-50 flex flex-col items-center justify-center rounded-lg border-2 border-red-200"
        >
          <div className="text-center text-red-600 px-4">
            <h3 className="text-lg font-semibold mb-2">Error</h3>
            <p className="text-sm">{error}</p>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default DIDAvatar;
