{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_5399b416._.js", "server/edge/chunks/node_modules_@auth_core_5ebafa38._.js", "server/edge/chunks/node_modules_jose_dist_webapi_49ff121e._.js", "server/edge/chunks/node_modules_e184ff1b._.js", "server/edge/chunks/[root-of-the-server]__df53d061._.js", "server/edge/chunks/edge-wrapper_3d09a47d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "EgkodQbEY9m1pIgxkdt6YkPs3VngzrM6PXdSaRub/rI=", "__NEXT_PREVIEW_MODE_ID": "d6ab3f13abd3684559b88dae4eee9f38", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9c137df6bba39f369b6d3706a783cff37dc6997b5e29eaffc4790b6968afe606", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ac18f8db828ccba68fcc7676f1665337c8a1e828bcc46427cea74833fed5854f"}}}, "sortedMiddleware": ["/"], "functions": {}}