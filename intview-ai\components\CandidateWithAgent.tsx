"use client";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { User, <PERSON><PERSON> } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import DIDAgent from "@/components/DIDAgent";
import CandidateImage from "@/components/CandidateImage";

type CandidateWithAgentProps = {
  className?: string;
  candidateName?: string;
  jobTitle?: string;
  useAgent?: boolean; 
  message?: string;
  onVideoReady?: () => void;
  onVideoEnd?: () => void;
};

const CandidateWithAgent: React.FC<CandidateWithAgentProps> = ({
  className = "",
  candidateName = "Jonathan",
  jobTitle = "Insurance Agent",
  useAgent = false,
}) => {
  const [isAgentMode, setIsAgentMode] = useState<boolean>(useAgent);

  

  return (
    <div className={`relative ${className}`}>
      {/* Mode Toggle Button */}
      <div className="absolute top-2 right-2 z-10">
        {/* <Button
          variant="outline"
          size="sm"
          onClick={toggleMode}
          className="bg-white/90 backdrop-blur-sm border-gray-200 hover:bg-white"
        >
          {isAgentMode ? (
            <>
              <User className="w-4 h-4 mr-1" />
              Static
            </>
          ) : (
            <>
              <Bot className="w-4 h-4 mr-1" />
              AI Agent
            </>
          )}
        </Button> */}
      </div>

      {/* Status Indicator */}
      {/* {isAgentMode && (
        <div className="absolute top-2 left-2 z-10">
          <div className="flex items-center space-x-2 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 border border-gray-200">
            <div className="w-2 h-2 rounded-full bg-blue-400 animate-pulse" />
            <span className="text-xs font-medium text-gray-700">
              AI Agent
            </span>
          </div>
        </div>
      )} */}

      {/* Content */}
      <AnimatePresence mode="wait">
        {isAgentMode ? (
          <motion.div
            key="agent"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className="w-full h-full"
          >
            <DIDAgent
              className={className}
              instructions={`You are an AI interview assistant conducting an interview for the ${jobTitle} position with ${candidateName}. Be professional, engaging, and ask relevant questions about their experience and qualifications.`}
              agentName={`${jobTitle} Interviewer`}
            />
          </motion.div>
        ) : (
          <motion.div
            key="static"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className="w-full h-full"
          >
            <CandidateImage className={className} />
            
            {/* Static Mode Info */}
            <div className="absolute bottom-2 left-2 right-2">
              <div className="bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-semibold text-gray-800">
                      {candidateName}
                    </h4>
                    <p className="text-xs text-gray-600">
                      {jobTitle} Candidate
                    </p>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full" />
                    <span className="text-xs text-gray-500">Static Mode</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>


    </div>
  );
};

export default CandidateWithAgent;
