"use client";
import React from "react";
import DIDAgent from "@/components/DIDAgent";

type CandidateWithAgentProps = {
  className?: string;
  candidateName?: string;
  jobTitle?: string;
};

const CandidateWithAgent: React.FC<CandidateWithAgentProps> = ({
  className = "",
  candidateName = "Jonathan",
  jobTitle = "Insurance Agent",
}) => {

  return (
    <div className={` ${className}`}>
      <DIDAgent
        className="w-[300px] h-full"
        instructions={`You are an AI interview assistant conducting an interview for the ${jobTitle} position with ${candidateName}. Be professional, engaging, and ask relevant questions about their experience and qualifications.`}
        agentName={`${jobTitle} Interviewer`}
      />
    </div>
  );
};

export default CandidateWithAgent;
