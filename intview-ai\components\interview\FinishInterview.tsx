import { <PERSON><PERSON><PERSON> } from "lucide-react";
import JobInfoCard from "@/components/JobInfoCard";
import QuestionsList from "@/components/QuestionsList";
import CandidateWithAgent from "@/components/CandidateWithAgent";
import InterviewLayout from "@/components/InterviewLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import VideoTranscript from "@/components/VideoTranscript";

type FinishInterviewProps = {
  onNext?: () => void;
};

const FinishInterview = ({ onNext }: FinishInterviewProps) => {
  return (
    <div className="h-screen">
      <JobInfoCard />

      <InterviewLayout>
        <div className="flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start">
          <QuestionsList />
          <CandidateWithAgent
            className="w-[265px] h-[365px]"
            useAgent={true}
            candidateName="<PERSON>"
            jobTitle="Insurance Agent"
            message="Thank you for completing the interview. Do you have any final questions?"
          />
          <VideoTranscript />
        </div>

        <div className="flex justify-center mt-10 gap-4">
          <Button
            variant="default"
            className="py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white"
            onClick={() => onNext && onNext()}
          >
            Finish Interview
            <ArrowRight className="w-6 h-6 duration-300 group-hover:translate-x-1" />
          </Button>
        </div>
      </InterviewLayout>
    </div>
  );
};

export default FinishInterview;
