"use client";
import { ArrowR<PERSON> } from "lucide-react";
import JobInfoCard from "@/components/JobInfoCard";
import QuestionsList from "@/components/QuestionsList";
import CandidateWithAgent from "@/components/CandidateWithAgent";
import InterviewLayout from "@/components/InterviewLayout";
import { Button } from "@/components/ui/button";

type QuestionsPageProps = {
  onNext?: () => void;
};

const QuestionsPage = ({ onNext }: QuestionsPageProps) => {
  return (
    <div className="h-screen">
      <JobInfoCard />

      <InterviewLayout>
        <div className="flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start">
          <QuestionsList className="h-[550px]" />
          <CandidateWithAgent
            className="h-[300px]"
            // useAgent={true}
            candidateName="Jonathan"
            jobTitle="Insurance Agent"
          />
        </div>

        <div className="flex justify-center mt-10 gap-4">
          <Button
            variant="default"
            size="lg"
            className="py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white"
            onClick={() => onNext && onNext()}
          >
            Start Interview
            <ArrowRight className="w-6 h-6 duration-300 group-hover:translate-x-1" />
          </Button>
        </div>
      </InterviewLayout>
    </div>
  );
};

export default QuestionsPage;
